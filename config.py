#!/usr/bin/env python3
"""
🔧 Configuration File for Scrapegraph AI Beer Scraper
====================================================

This file contains all the configuration settings for the beer scraper.
Modify these settings according to your needs.

Author: Augment Agent
Date: 2025-06-14
"""

# 🔑 API Configuration
# Replace with your actual Scrapegraph AI API key
SCRAPEGRAPH_API_KEY = "your-sgai-api-key"

# 🌐 Scraping Configuration
SCRAPING_CONFIG = {
    # Maximum number of pages to scrape
    "max_pages": 20,
    
    # Delay between requests (seconds) - be respectful to the server!
    "delay_seconds": 2.0,
    
    # Base URL for Tesco beer search
    "base_url": "https://www.tesco.com/groceries/en-GB/search",
    
    # Search query
    "search_query": "beer",
    
    # Timeout for requests (seconds)
    "request_timeout": 30,
    
    # Retry attempts for failed requests
    "max_retries": 3,
}

# 📁 Output Configuration
OUTPUT_CONFIG = {
    # Directory to save output files
    "output_directory": ".",
    
    # File naming pattern (timestamp will be added automatically)
    "json_filename_pattern": "scrapegraph_beer_products_{timestamp}.json",
    "csv_filename_pattern": "scrapegraph_beer_products_{timestamp}.csv",
    
    # Include raw data in JSON output
    "include_raw_data": True,
    
    # CSV columns to export
    "csv_columns": [
        'name', 'brand', 'pack_size', 'volume_ml', 'price', 
        'alcohol_content', 'image_url', 'product_url', 'retailer',
        'scraped_page', 'scraped_at'
    ]
}

# 🔍 Data Extraction Configuration
EXTRACTION_CONFIG = {
    # Enhanced prompt for better extraction
    "user_prompt": """
    Extract ALL beer products from this page with the following information for each beer:
    
    1. Product name (full name including pack size, e.g., "Budweiser Premium 4 x 440ml")
    2. Brand name (e.g., "Budweiser")
    3. Pack size (e.g., "4 pack", "1 unit", "12 pack")
    4. Volume per unit (e.g., "440ml", "500ml")
    5. Price (if available)
    6. Image URL (product image)
    7. Product URL (link to product page)
    8. Alcohol content (if visible)
    
    IMPORTANT: 
    - Treat each pack size as a SEPARATE product (e.g., Budweiser 1-pack and Budweiser 4-pack are different entries)
    - Extract the main product image URL
    - Include ALL beers visible on the page
    - If a product has multiple variants (different sizes), list each separately
    
    Return the data in a structured JSON format with an array of products.
    """,
    
    # Common beer brands for better extraction
    "known_brands": [
        'Stella Artois', 'Heineken', 'Budweiser', 'Corona', 'Carlsberg',
        'Guinness', 'Peroni', 'Beck\'s', 'Kronenbourg', 'Foster\'s',
        'Carling', 'John Smith\'s', 'Strongbow', 'Magners', 'Bulmers',
        'Amstel', 'San Miguel', 'Desperados', 'Tiger', 'Asahi',
        'Sapporo', 'Kirin', 'Tsingtao', 'Efes', 'Moretti'
    ]
}

# 📊 Logging Configuration
LOGGING_CONFIG = {
    # Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    "level": "INFO",
    
    # Log file name
    "log_file": "scrapegraph_multi_page_scraper.log",
    
    # Log format
    "format": "%(asctime)s - %(levelname)s - %(message)s",
    
    # Enable console logging
    "console_logging": True,
    
    # Enable file logging
    "file_logging": True
}

# 🛡️ Safety Configuration
SAFETY_CONFIG = {
    # Stop scraping if this many consecutive pages return no results
    "empty_page_threshold": 3,
    
    # Maximum total products to extract (safety limit)
    "max_total_products": 10000,
    
    # User agent for requests
    "user_agent": "ScrapegraphAI-BeerScraper/1.0",
    
    # Respect robots.txt
    "respect_robots_txt": True
}

# 🧪 Development Configuration
DEV_CONFIG = {
    # Enable debug mode
    "debug_mode": False,
    
    # Save raw responses for debugging
    "save_raw_responses": False,
    
    # Test mode (only scrape first few pages)
    "test_mode": False,
    "test_max_pages": 3
}


def get_config():
    """
    Get the complete configuration dictionary.
    
    Returns:
        dict: Complete configuration
    """
    return {
        "api_key": SCRAPEGRAPH_API_KEY,
        "scraping": SCRAPING_CONFIG,
        "output": OUTPUT_CONFIG,
        "extraction": EXTRACTION_CONFIG,
        "logging": LOGGING_CONFIG,
        "safety": SAFETY_CONFIG,
        "dev": DEV_CONFIG
    }


def validate_config():
    """
    Validate the configuration settings.
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if SCRAPEGRAPH_API_KEY == "your-sgai-api-key":
        return False, "Please set your Scrapegraph AI API key in config.py"
    
    if SCRAPING_CONFIG["max_pages"] <= 0:
        return False, "max_pages must be greater than 0"
    
    if SCRAPING_CONFIG["delay_seconds"] < 0:
        return False, "delay_seconds must be non-negative"
    
    return True, "Configuration is valid"


if __name__ == "__main__":
    # Test configuration
    is_valid, message = validate_config()
    if is_valid:
        print("✅ Configuration is valid!")
        config = get_config()
        print(f"📄 Max pages: {config['scraping']['max_pages']}")
        print(f"⏱️ Delay: {config['scraping']['delay_seconds']} seconds")
    else:
        print(f"❌ Configuration error: {message}")
