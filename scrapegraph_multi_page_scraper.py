#!/usr/bin/env python3
"""
🍺 Scrapegraph AI Multi-Page Beer Scraper
==========================================

A comprehensive beer scraper using Scrapegraph AI that extracts beer data
from ALL pages of Tesco's beer section, treating each pack size as individual entries.

Features:
- Multi-page scraping with automatic pagination
- Individual entries for different pack sizes
- Image URL extraction
- Comprehensive logging
- Multiple output formats (JSON, CSV)
- Error handling and retry logic

Author: Augment Agent
Date: 2025-06-14
"""

import json
import csv
import logging
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
import re
from pathlib import Path

from scrapegraph_py import Client
from scrapegraph_py.logger import sgai_logger

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scrapegraph_multi_page_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Set Scrapegraph AI logging
sgai_logger.set_logging(level="INFO")


class ScrapegraphMultiPageBeerScraper:
    """
    A comprehensive beer scraper using Scrapegraph AI for multi-page extraction.
    """
    
    def __init__(self, api_key: str):
        """
        Initialize the scraper with Scrapegraph AI client.
        
        Args:
            api_key (str): Your Scrapegraph AI API key
        """
        self.client = Client(api_key=api_key)
        self.base_url = "https://www.tesco.com/groceries/en-GB/search"
        self.all_beers = []
        self.processed_pages = 0
        self.total_products = 0
        
        logger.info("🍺 Scrapegraph Multi-Page Beer Scraper initialized")
    
    def generate_page_urls(self, max_pages: int = 50) -> List[str]:
        """
        Generate URLs for multiple pages of beer search results.
        
        Args:
            max_pages (int): Maximum number of pages to scrape
            
        Returns:
            List[str]: List of URLs to scrape
        """
        urls = []
        
        # First page (no page parameter)
        urls.append(f"{self.base_url}?query=beer&inputType=suggested")
        
        # Additional pages
        for page in range(2, max_pages + 1):
            urls.append(f"{self.base_url}?query=beer&inputType=suggested&page={page}")
        
        logger.info(f"Generated {len(urls)} URLs for scraping")
        return urls
    
    def extract_beer_data(self, url: str, page_num: int) -> List[Dict[str, Any]]:
        """
        Extract beer data from a single page using Scrapegraph AI.
        
        Args:
            url (str): URL to scrape
            page_num (int): Page number for logging
            
        Returns:
            List[Dict]: List of beer products extracted
        """
        logger.info(f"🔍 Scraping page {page_num}: {url}")
        
        # Enhanced prompt for better extraction
        user_prompt = """
        Extract ALL beer products from this page with the following information for each beer:
        
        1. Product name (full name including pack size, e.g., "Budweiser Premium 4 x 440ml")
        2. Brand name (e.g., "Budweiser")
        3. Pack size (e.g., "4 pack", "1 unit", "12 pack")
        4. Volume per unit (e.g., "440ml", "500ml")
        5. Price (if available)
        6. Image URL (product image)
        7. Product URL (link to product page)
        8. Alcohol content (if visible)
        
        IMPORTANT: 
        - Treat each pack size as a SEPARATE product (e.g., Budweiser 1-pack and Budweiser 4-pack are different entries)
        - Extract the main product image URL
        - Include ALL beers visible on the page
        - If a product has multiple variants (different sizes), list each separately
        
        Return the data in a structured JSON format with an array of products.
        """
        
        try:
            # Make the request to Scrapegraph AI
            response = self.client.smartscraper(
                website_url=url,
                user_prompt=user_prompt
            )
            
            logger.info(f"✅ Page {page_num} - Request ID: {response.get('request_id', 'N/A')}")
            
            # Parse the result
            result = response.get('result', '')
            if not result:
                logger.warning(f"⚠️ Page {page_num} - No result returned")
                return []
            
            # Try to parse JSON result
            try:
                if isinstance(result, str):
                    # Try to extract JSON from the string
                    json_match = re.search(r'\[.*\]', result, re.DOTALL)
                    if json_match:
                        beer_data = json.loads(json_match.group())
                    else:
                        # If no JSON array found, try to parse the whole result
                        beer_data = json.loads(result)
                else:
                    beer_data = result
                
                # Ensure we have a list
                if isinstance(beer_data, dict):
                    beer_data = [beer_data]
                elif not isinstance(beer_data, list):
                    logger.warning(f"⚠️ Page {page_num} - Unexpected data format")
                    return []
                
                # Add metadata to each product
                for beer in beer_data:
                    beer['scraped_page'] = page_num
                    beer['scraped_url'] = url
                    beer['scraped_at'] = datetime.now().isoformat()
                    beer['retailer'] = 'Tesco'
                
                logger.info(f"📦 Page {page_num} - Extracted {len(beer_data)} products")
                return beer_data
                
            except json.JSONDecodeError as e:
                logger.error(f"❌ Page {page_num} - JSON parsing error: {e}")
                logger.debug(f"Raw result: {result[:500]}...")
                return []
                
        except Exception as e:
            logger.error(f"❌ Page {page_num} - Scraping error: {e}")
            return []
    
    def scrape_all_pages(self, max_pages: int = 20, delay: float = 2.0) -> List[Dict[str, Any]]:
        """
        Scrape beer data from all pages.
        
        Args:
            max_pages (int): Maximum number of pages to scrape
            delay (float): Delay between requests in seconds
            
        Returns:
            List[Dict]: All extracted beer products
        """
        logger.info(f"🚀 Starting multi-page scraping (max {max_pages} pages)")
        
        urls = self.generate_page_urls(max_pages)
        all_products = []
        
        for i, url in enumerate(urls, 1):
            try:
                # Extract data from current page
                page_products = self.extract_beer_data(url, i)
                
                if not page_products:
                    logger.warning(f"⚠️ Page {i} returned no products - might be end of results")
                    # If we get 3 consecutive empty pages, stop scraping
                    if i > 3 and not any(self.all_beers[-3:]):
                        logger.info("🛑 Stopping scraping - reached end of results")
                        break
                else:
                    all_products.extend(page_products)
                    self.processed_pages += 1
                    logger.info(f"✅ Page {i} complete - {len(page_products)} products added")
                
                # Respectful delay between requests
                if i < len(urls):
                    logger.info(f"⏳ Waiting {delay} seconds before next page...")
                    time.sleep(delay)
                    
            except KeyboardInterrupt:
                logger.info("🛑 Scraping interrupted by user")
                break
            except Exception as e:
                logger.error(f"❌ Error processing page {i}: {e}")
                continue
        
        self.all_beers = all_products
        self.total_products = len(all_products)
        
        logger.info(f"🎉 Scraping complete! Total products: {self.total_products} from {self.processed_pages} pages")
        return all_products

    def clean_and_process_data(self) -> List[Dict[str, Any]]:
        """
        Clean and process the extracted beer data.

        Returns:
            List[Dict]: Cleaned and processed beer data
        """
        logger.info("🧹 Cleaning and processing beer data...")

        processed_beers = []

        for beer in self.all_beers:
            try:
                # Create a standardized beer entry
                processed_beer = {
                    'name': self._clean_text(beer.get('name', beer.get('product_name', 'Unknown'))),
                    'brand': self._extract_brand(beer.get('brand', beer.get('name', ''))),
                    'pack_size': self._extract_pack_size(beer.get('pack_size', beer.get('name', ''))),
                    'volume_ml': self._extract_volume(beer.get('volume', beer.get('name', ''))),
                    'price': self._extract_price(beer.get('price', '')),
                    'alcohol_content': self._extract_alcohol_content(beer.get('alcohol_content', beer.get('name', ''))),
                    'image_url': beer.get('image_url', beer.get('image', '')),
                    'product_url': beer.get('product_url', beer.get('url', '')),
                    'retailer': beer.get('retailer', 'Tesco'),
                    'scraped_page': beer.get('scraped_page', 0),
                    'scraped_at': beer.get('scraped_at', datetime.now().isoformat()),
                    'raw_data': beer  # Keep original data for reference
                }

                processed_beers.append(processed_beer)

            except Exception as e:
                logger.warning(f"⚠️ Error processing beer entry: {e}")
                continue

        logger.info(f"✅ Processed {len(processed_beers)} beer entries")
        return processed_beers

    def _clean_text(self, text: str) -> str:
        """Clean and normalize text."""
        if not text:
            return ""
        return re.sub(r'\s+', ' ', str(text).strip())

    def _extract_brand(self, text: str) -> str:
        """Extract brand name from text."""
        if not text:
            return "Unknown"

        # Common beer brands
        brands = [
            'Stella Artois', 'Heineken', 'Budweiser', 'Corona', 'Carlsberg',
            'Guinness', 'Peroni', 'Beck\'s', 'Kronenbourg', 'Foster\'s',
            'Carling', 'John Smith\'s', 'Strongbow', 'Magners', 'Bulmers'
        ]

        text_upper = text.upper()
        for brand in brands:
            if brand.upper() in text_upper:
                return brand

        # If no known brand found, try to extract first word(s)
        words = text.split()
        if words:
            return words[0]

        return "Unknown"

    def _extract_pack_size(self, text: str) -> str:
        """Extract pack size from text."""
        if not text:
            return "1 unit"

        # Look for pack size patterns
        pack_patterns = [
            r'(\d+)\s*x\s*\d+ml',  # "4 x 440ml"
            r'(\d+)\s*pack',       # "4 pack"
            r'(\d+)\s*cans?',      # "4 cans"
            r'(\d+)\s*bottles?',   # "4 bottles"
        ]

        for pattern in pack_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                count = int(match.group(1))
                return f"{count} pack" if count > 1 else "1 unit"

        return "1 unit"

    def _extract_volume(self, text: str) -> Optional[int]:
        """Extract volume in ml from text."""
        if not text:
            return None

        # Look for volume patterns
        volume_patterns = [
            r'(\d+)ml',
            r'(\d+)\s*ml',
            r'(\d+\.\d+)l',  # Convert liters to ml
        ]

        for pattern in volume_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                volume = float(match.group(1))
                if 'l' in pattern:  # Convert liters to ml
                    volume *= 1000
                return int(volume)

        return None

    def _extract_price(self, text: str) -> Optional[float]:
        """Extract price from text."""
        if not text:
            return None

        # Look for price patterns
        price_patterns = [
            r'£(\d+\.\d+)',
            r'£(\d+)',
            r'(\d+\.\d+)',
        ]

        for pattern in price_patterns:
            match = re.search(pattern, str(text))
            if match:
                return float(match.group(1))

        return None

    def _extract_alcohol_content(self, text: str) -> Optional[float]:
        """Extract alcohol content percentage from text."""
        if not text:
            return None

        # Look for alcohol content patterns
        alcohol_patterns = [
            r'(\d+\.\d+)%',
            r'(\d+)%',
            r'(\d+\.\d+)\s*abv',
        ]

        for pattern in alcohol_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return float(match.group(1))

        return None

    def save_to_json(self, filename: Optional[str] = None) -> str:
        """
        Save beer data to JSON file.

        Args:
            filename (str, optional): Custom filename

        Returns:
            str: Path to saved file
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"scrapegraph_beer_products_{timestamp}.json"

        processed_data = self.clean_and_process_data()

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(processed_data, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Saved {len(processed_data)} products to {filename}")
        return filename

    def save_to_csv(self, filename: Optional[str] = None) -> str:
        """
        Save beer data to CSV file.

        Args:
            filename (str, optional): Custom filename

        Returns:
            str: Path to saved file
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"scrapegraph_beer_products_{timestamp}.csv"

        processed_data = self.clean_and_process_data()

        if not processed_data:
            logger.warning("⚠️ No data to save")
            return filename

        # Define CSV columns
        columns = [
            'name', 'brand', 'pack_size', 'volume_ml', 'price',
            'alcohol_content', 'image_url', 'product_url', 'retailer',
            'scraped_page', 'scraped_at'
        ]

        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=columns)
            writer.writeheader()

            for beer in processed_data:
                # Only write the columns we want (exclude raw_data)
                row = {col: beer.get(col, '') for col in columns}
                writer.writerow(row)

        logger.info(f"📊 Saved {len(processed_data)} products to {filename}")
        return filename

    def print_summary(self):
        """Print a summary of scraped data."""
        if not self.all_beers:
            logger.info("📊 No data to summarize")
            return

        processed_data = self.clean_and_process_data()

        print("\n" + "="*60)
        print("🍺 SCRAPEGRAPH AI BEER SCRAPING SUMMARY 🍺")
        print("="*60)
        print(f"📦 Total Products: {len(processed_data)}")
        print(f"📄 Pages Scraped: {self.processed_pages}")
        print(f"🏪 Retailer: Tesco")
        print(f"⏰ Scraped At: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Brand analysis
        brands = {}
        pack_sizes = {}

        for beer in processed_data:
            brand = beer.get('brand', 'Unknown')
            pack_size = beer.get('pack_size', 'Unknown')

            brands[brand] = brands.get(brand, 0) + 1
            pack_sizes[pack_size] = pack_sizes.get(pack_size, 0) + 1

        print(f"\n🏷️ Top Brands:")
        for brand, count in sorted(brands.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  - {brand}: {count} products")

        print(f"\n📦 Pack Sizes:")
        for pack_size, count in sorted(pack_sizes.items(), key=lambda x: x[1], reverse=True):
            print(f"  - {pack_size}: {count} products")

        # Sample products
        print(f"\n🍻 Sample Products:")
        for i, beer in enumerate(processed_data[:5], 1):
            print(f"{i}. {beer.get('name', 'Unknown')}")
            print(f"   Brand: {beer.get('brand', 'Unknown')} | Pack: {beer.get('pack_size', 'Unknown')}")
            if beer.get('price'):
                print(f"   Price: £{beer.get('price')} | Volume: {beer.get('volume_ml', 'Unknown')}ml")
            if beer.get('image_url'):
                print(f"   Image: {beer.get('image_url')[:50]}...")
            print()

        print("="*60)

    def close(self):
        """Close the Scrapegraph AI client."""
        if self.client:
            self.client.close()
            logger.info("🔒 Scrapegraph AI client closed")


def main():
    """
    Main execution function.
    """
    print("🍺 Scrapegraph AI Multi-Page Beer Scraper 🍺")
    print("=" * 50)

    # You need to replace this with your actual API key
    API_KEY = "your-sgai-api-key"

    if API_KEY == "your-sgai-api-key":
        print("❌ Please set your Scrapegraph AI API key in the script!")
        print("   Replace 'your-sgai-api-key' with your actual API key.")
        return

    try:
        # Initialize scraper
        scraper = ScrapegraphMultiPageBeerScraper(API_KEY)

        # Scrape all pages (adjust max_pages as needed)
        print("🚀 Starting multi-page beer scraping...")
        all_beers = scraper.scrape_all_pages(max_pages=10, delay=2.0)

        if all_beers:
            # Save results
            json_file = scraper.save_to_json()
            csv_file = scraper.save_to_csv()

            # Print summary
            scraper.print_summary()

            print(f"\n💾 Results saved to:")
            print(f"   📄 JSON: {json_file}")
            print(f"   📊 CSV: {csv_file}")

        else:
            print("❌ No beer data was extracted. Check the logs for details.")

    except KeyboardInterrupt:
        print("\n🛑 Scraping interrupted by user")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        print(f"❌ An error occurred: {e}")
    finally:
        # Always close the client
        if 'scraper' in locals():
            scraper.close()


if __name__ == "__main__":
    main()
