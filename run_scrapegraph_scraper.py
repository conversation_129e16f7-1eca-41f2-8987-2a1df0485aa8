#!/usr/bin/env python3
"""
🍺 Quick Start Script for Scrapegraph AI Beer Scraper
====================================================

This script demonstrates how to use the Scrapegraph AI multi-page beer scraper
to extract beer data from all pages of Tesco's beer section.

Usage:
1. Set your Scrapegraph AI API key below
2. Run: python run_scrapegraph_scraper.py
3. Check the generated CSV and JSON files

Author: Augment Agent
Date: 2025-06-14
"""

from scrapegraph_multi_page_scraper import ScrapegraphMultiPageBeerScraper
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    """
    Main function to run the beer scraper.
    """
    print("🍺 Scrapegraph AI Beer Scraper - Quick Start 🍺")
    print("=" * 55)
    
    # ⚠️ IMPORTANT: Replace with your actual Scrapegraph AI API key
    API_KEY = "your-sgai-api-key"
    
    # Configuration
    MAX_PAGES = 15  # Adjust based on how many pages you want to scrape
    DELAY_SECONDS = 2.0  # Delay between requests (be respectful!)
    
    if API_KEY == "your-sgai-api-key":
        print("❌ ERROR: Please set your Scrapegraph AI API key!")
        print("\n📝 To get started:")
        print("1. Open 'run_scrapegraph_scraper.py' in your editor")
        print("2. Replace 'your-sgai-api-key' with your actual API key")
        print("3. Run the script again")
        print("\n🔑 Get your API key from: https://scrapegraph.ai/")
        return
    
    try:
        print(f"🚀 Initializing scraper...")
        print(f"📄 Max pages to scrape: {MAX_PAGES}")
        print(f"⏱️ Delay between requests: {DELAY_SECONDS} seconds")
        print()
        
        # Initialize the scraper
        scraper = ScrapegraphMultiPageBeerScraper(API_KEY)
        
        # Start scraping
        print("🔍 Starting beer extraction from Tesco...")
        all_beers = scraper.scrape_all_pages(
            max_pages=MAX_PAGES, 
            delay=DELAY_SECONDS
        )
        
        if all_beers:
            print(f"\n✅ Successfully extracted {len(all_beers)} beer products!")
            
            # Save the results
            print("💾 Saving results...")
            json_file = scraper.save_to_json()
            csv_file = scraper.save_to_csv()
            
            # Show summary
            scraper.print_summary()
            
            print(f"\n🎉 Scraping completed successfully!")
            print(f"📁 Files saved:")
            print(f"   📄 JSON: {json_file}")
            print(f"   📊 CSV: {csv_file}")
            print(f"\n💡 You can now:")
            print(f"   - Open {csv_file} in Excel/Google Sheets")
            print(f"   - Use {json_file} for further data processing")
            print(f"   - Check 'scrapegraph_multi_page_scraper.log' for detailed logs")
            
        else:
            print("❌ No beer data was extracted.")
            print("💡 Possible issues:")
            print("   - Invalid API key")
            print("   - Network connectivity problems")
            print("   - Website structure changes")
            print("   - Check the log file for detailed error messages")
    
    except KeyboardInterrupt:
        print("\n🛑 Scraping interrupted by user (Ctrl+C)")
        print("💡 Partial results may have been saved")
    
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"\n❌ An error occurred: {e}")
        print("💡 Check the log file for more details")
    
    finally:
        # Always close the client
        if 'scraper' in locals():
            scraper.close()
            print("🔒 Scraper client closed")


if __name__ == "__main__":
    main()
