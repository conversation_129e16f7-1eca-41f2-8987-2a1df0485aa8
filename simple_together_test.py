#!/usr/bin/env python3
"""
Simple test of Together AI with Scrapegraph AI
"""

import os
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_together_ai_integration():
    """Test Together AI integration with Scrapegraph AI"""
    
    print("🤖 Testing Together AI Integration with Scrapegraph AI")
    print("=" * 60)
    
    # Set API key
    together_key = "73b7dec43b6d5d27bfe0943ec41e7a901e26035b941db5db186e827b7185b7f7"
    os.environ["TOGETHER_API_KEY"] = together_key
    
    try:
        print("1. Testing langchain-openai import...")
        from langchain_openai import ChatOpenAI
        print("✅ langchain-openai imported successfully")
        
        print("\n2. Creating Together AI model instance...")
        llm_model_instance = ChatOpenAI(
            model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
            api_key=together_key,
            base_url="https://api.together.xyz/v1",
            temperature=0.1,
            max_tokens=1000,
        )
        print("✅ Together AI model instance created")
        
        print("\n3. Testing basic model call...")
        response = llm_model_instance.invoke("Hello! Please respond with 'Together AI is working with Scrapegraph!'")
        print(f"✅ Model response: {response.content}")
        
        print("\n4. Testing Scrapegraph AI import...")
        from scrapegraphai.graphs import SmartScraperGraph
        print("✅ Scrapegraph AI imported successfully")
        
        print("\n5. Creating Scrapegraph AI configuration...")
        graph_config = {
            "llm": {
                "model_instance": llm_model_instance,
                "model_tokens": 8192,
            },
            "verbose": True,
            "headless": True,
        }
        print("✅ Configuration created")
        
        print("\n6. Testing simple scraping...")
        prompt = """
        Extract any text content from this webpage.
        Return the result as a JSON object with a 'content' field.
        """
        
        # Use a simple test page
        test_url = "https://httpbin.org/html"
        
        print(f"   Testing with URL: {test_url}")
        
        smart_scraper = SmartScraperGraph(
            prompt=prompt,
            source=test_url,
            config=graph_config
        )
        
        print("   Running scraper...")
        result = smart_scraper.run()
        
        print("✅ Scraping completed!")
        print(f"   Result type: {type(result)}")
        if result:
            print(f"   Result preview: {str(result)[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    
    success = test_together_ai_integration()
    
    if success:
        print("\n🎉 SUCCESS!")
        print("Together AI + Scrapegraph AI integration is working!")
        print("\nYou can now run the full beer scraper:")
        print("python scrapegraph_beer_scraper.py")
    else:
        print("\n❌ FAILED!")
        print("There was an issue with the integration.")

if __name__ == "__main__":
    main()
