#!/usr/bin/env python3
"""
Automatic Beer Scraper using Together AI
========================================

This script automatically runs the beer scraper using Together AI
without requiring user input.
"""

import os
import json
import csv
import time
from datetime import datetime
from typing import List, Dict, Optional
from dataclasses import dataclass, asdict
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_beer_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class BeerProduct:
    """Data class for beer product information"""
    name: str
    brand: str
    pack_size: str
    volume_ml: Optional[int]
    alcohol_content: Optional[float]
    price: Optional[float]
    price_per_unit: Optional[float]
    retailer: str
    product_url: str
    image_url: Optional[str]
    availability: str
    description: Optional[str]
    scraped_at: str

class AutoBeerScraper:
    """Automatic beer scraper using Together AI"""
    
    def __init__(self):
        """Initialize the scraper with Together AI"""
        self.setup_together_ai()
        self.products = []
        
    def setup_together_ai(self):
        """Setup Together AI configuration"""
        together_key = "73b7dec43b6d5d27bfe0943ec41e7a901e26035b941db5db186e827b7185b7f7"
        
        try:
            from langchain_openai import ChatOpenAI
            
            llm_model_instance = ChatOpenAI(
                model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
                api_key=together_key,
                base_url="https://api.together.xyz/v1",
                temperature=0.1,
                max_tokens=8192,
            )
            
            self.graph_config = {
                "llm": {
                    "model_instance": llm_model_instance,
                    "model_tokens": 8192,
                },
                "verbose": True,
                "headless": True,
            }
            logger.info("Together AI configured successfully")
            
        except Exception as e:
            logger.error(f"Error setting up Together AI: {e}")
            raise
    
    def create_beer_extraction_prompt(self) -> str:
        """Create prompt for extracting beer products"""
        return """
        Extract ALL beer products from this webpage with detailed information:
        
        For each beer product found, provide:
        1. Product name (full name including brand and description)
        2. Brand name (e.g., Stella Artois, Heineken, Corona)
        3. Pack size information (e.g., "1 unit", "4 pack", "12 pack", "24 pack")
        4. Volume per unit in milliliters (e.g., 330, 440, 500)
        5. Alcohol content percentage (e.g., 4.5, 5.0)
        6. Price in GBP (pounds)
        7. Product URL/link
        8. Image URL if available
        9. Availability status (Available, Out of Stock, etc.)
        10. Any additional product description
        
        IMPORTANT INSTRUCTIONS:
        - If a product is a multi-pack (e.g., 4-pack, 12-pack), create SEPARATE entries for each individual beer
        - For a 4-pack, create 4 separate entries with pack_size "1 unit" and calculate individual price
        - For a 12-pack, create 12 separate entries with pack_size "1 unit" and calculate individual price
        - Extract volume information from product names (look for ml, cl, l indicators)
        - Extract alcohol content from product descriptions (look for % ABV indicators)
        - Be thorough and don't miss any beer products on the page
        
        Return the data as a JSON array where each object represents ONE individual beer unit.
        """
    
    def scrape_tesco_beers(self, max_pages: int = 3) -> List[Dict]:
        """Scrape beer products from Tesco"""
        try:
            from scrapegraphai.graphs import SmartScraperGraph
        except ImportError:
            logger.error("Scrapegraph AI not installed")
            return []
        
        all_products = []
        prompt = self.create_beer_extraction_prompt()
        
        logger.info("Starting Tesco beer scraping...")
        
        for page in range(1, max_pages + 1):
            try:
                url = f"https://www.tesco.com/groceries/en-GB/shop/beer-wine-spirit/beer?page={page}"
                logger.info(f"Scraping Tesco page {page}: {url}")
                
                smart_scraper = SmartScraperGraph(
                    prompt=prompt,
                    source=url,
                    config=self.graph_config
                )
                
                result = smart_scraper.run()
                
                if not result:
                    logger.warning(f"No data returned from Tesco page {page}")
                    continue
                
                # Handle different result formats
                if isinstance(result, dict):
                    if 'products' in result:
                        products = result['products']
                    elif 'beers' in result:
                        products = result['beers']
                    elif isinstance(result.get('content'), list):
                        products = result['content']
                    else:
                        products = [result]
                elif isinstance(result, list):
                    products = result
                else:
                    logger.warning(f"Unexpected result format from Tesco page {page}")
                    continue
                
                if not products:
                    logger.info(f"No products found on Tesco page {page}")
                    break
                
                # Add retailer info
                for product in products:
                    if isinstance(product, dict):
                        product['retailer'] = 'Tesco'
                        product['scraped_at'] = datetime.now().isoformat()
                        all_products.append(product)
                
                logger.info(f"Extracted {len(products)} products from Tesco page {page}")
                time.sleep(2)  # Respectful delay
                
            except Exception as e:
                logger.error(f"Error scraping Tesco page {page}: {e}")
                continue
        
        logger.info(f"Total products from Tesco: {len(all_products)}")
        return all_products
    
    def normalize_products(self, raw_products: List[Dict]) -> List[BeerProduct]:
        """Convert raw products to BeerProduct objects"""
        normalized = []
        
        for product in raw_products:
            try:
                beer = BeerProduct(
                    name=str(product.get('name', 'Unknown')),
                    brand=str(product.get('brand', 'Unknown')),
                    pack_size=str(product.get('pack_size', '1 unit')),
                    volume_ml=product.get('volume_ml'),
                    alcohol_content=product.get('alcohol_content'),
                    price=product.get('price'),
                    price_per_unit=product.get('price_per_unit', product.get('price')),
                    retailer=product.get('retailer', 'Unknown'),
                    product_url=product.get('product_url', ''),
                    image_url=product.get('image_url', ''),
                    availability=product.get('availability', 'Unknown'),
                    description=product.get('description', ''),
                    scraped_at=product.get('scraped_at', datetime.now().isoformat())
                )
                normalized.append(beer)
            except Exception as e:
                logger.warning(f"Error normalizing product: {e}")
                continue
        
        return normalized
    
    def save_results(self, products: List[BeerProduct]):
        """Save results to CSV and JSON"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save to CSV
        csv_filename = f"together_ai_beer_products_{timestamp}.csv"
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            if products:
                fieldnames = list(asdict(products[0]).keys())
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for product in products:
                    writer.writerow(asdict(product))
        
        # Save to JSON
        json_filename = f"together_ai_beer_products_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as jsonfile:
            products_dict = [asdict(product) for product in products]
            json.dump(products_dict, jsonfile, indent=2, ensure_ascii=False)
        
        logger.info(f"Results saved to {csv_filename} and {json_filename}")
        return csv_filename, json_filename
    
    def run(self):
        """Run the automatic beer scraper"""
        print("🍺 AUTOMATIC BEER SCRAPER WITH TOGETHER AI")
        print("=" * 60)
        print("Using Llama 3.3 70B Instruct Turbo Free model")
        print("Creating individual entries for each beer in multi-packs")
        
        try:
            # Scrape products
            raw_products = self.scrape_tesco_beers(max_pages=3)
            
            if not raw_products:
                print("❌ No products found!")
                return
            
            # Normalize products
            products = self.normalize_products(raw_products)
            
            if not products:
                print("❌ No valid products after normalization!")
                return
            
            print(f"\n✅ Successfully scraped {len(products)} beer products!")
            
            # Save results
            csv_file, json_file = self.save_results(products)
            
            # Show summary
            print(f"\n📊 Summary:")
            print(f"   Total individual beer entries: {len(products)}")
            
            brands = {}
            for product in products:
                brands[product.brand] = brands.get(product.brand, 0) + 1
            
            print(f"   Unique brands: {len(brands)}")
            top_brands = sorted(brands.items(), key=lambda x: x[1], reverse=True)[:5]
            for brand, count in top_brands:
                print(f"     {brand}: {count} beers")
            
            print(f"\n📁 Files created:")
            print(f"   CSV: {csv_file}")
            print(f"   JSON: {json_file}")
            print(f"   Log: auto_beer_scraper.log")
            
            print(f"\n🎉 Scraping completed successfully!")
            
        except Exception as e:
            logger.error(f"Error in main execution: {e}")
            print(f"❌ Error: {e}")

def main():
    """Main function"""
    scraper = AutoBeerScraper()
    scraper.run()

if __name__ == "__main__":
    main()
